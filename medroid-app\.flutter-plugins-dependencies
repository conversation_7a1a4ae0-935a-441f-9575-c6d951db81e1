{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "agora_rtc_engine", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\agora_rtc_engine-6.5.2\\\\", "native_build": true, "dependencies": ["iris_method_channel"], "dev_dependency": false}, {"name": "audioplayers_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audioplayers_darwin-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-4.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-14.9.4\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility-6.0.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geocoding_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geocoding_ios-2.3.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.3.13\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_sign_in_ios-5.9.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_ios-0.8.12+2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "iris_method_channel", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\iris_method_channel-2.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-4.2.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.4.7\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "pointer_interceptor_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pointer_interceptor_ios-0.10.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "record_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\record_darwin-1.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sign_in_with_apple-6.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "stripe_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\stripe_ios-9.6.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_ios-6.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.7.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.1\\\\", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "android": [{"name": "agora_rtc_engine", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\agora_rtc_engine-6.5.2\\\\", "native_build": true, "dependencies": ["iris_method_channel"], "dev_dependency": false}, {"name": "audioplayers_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audioplayers_android-4.0.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-4.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-14.9.4\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility-6.0.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.28\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geocoding_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geocoding_android-3.3.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_android-4.6.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_sign_in_android-6.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_android-0.8.12+23\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "iris_method_channel", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\iris_method_channel-2.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-4.2.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_android-12.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "record_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\record_android-1.3.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.10\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sign_in_with_apple-6.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_android-2.4.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "stripe_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\stripe_android-9.6.0+2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_android-6.3.16\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_android-2.8.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.1\\\\", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "macos": [{"name": "agora_rtc_engine", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\agora_rtc_engine-6.5.2\\\\", "native_build": true, "dependencies": ["iris_method_channel"], "dev_dependency": false}, {"name": "audioplayers_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audioplayers_darwin-5.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-4.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_macos-0.9.4+2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging-14.9.4\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_macos-1.0.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.3.13\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_sign_in_ios-5.9.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_macos-0.2.1+2\\\\", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "iris_method_channel", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\iris_method_channel-2.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-4.2.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "record_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\record_darwin-1.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sign_in_with_apple-6.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_macos-3.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.7.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.1\\\\", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "linux": [{"name": "audioplayers_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audioplayers_linux-3.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-4.0.2\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_linux-0.9.3+2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_linux-1.0.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_linux-0.2.1+2\\\\", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-4.2.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "record_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\record_linux-0.7.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": false, "dependencies": ["url_launcher_linux"], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_linux-3.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.1\\\\", "native_build": false, "dependencies": ["package_info_plus"], "dev_dependency": false}], "windows": [{"name": "agora_rtc_engine", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\agora_rtc_engine-6.5.2\\\\", "native_build": true, "dependencies": ["iris_method_channel"], "dev_dependency": false}, {"name": "audioplayers_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audioplayers_windows-3.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-4.0.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_selector_windows-0.9.3+4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_windows-1.0.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_windows-0.2.5\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_windows-0.2.1+1\\\\", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "iris_method_channel", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\iris_method_channel-2.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-4.2.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "record_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\record_windows-1.0.6\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "native_build": true, "dependencies": ["url_launcher_windows"], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_windows-3.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.1\\\\", "native_build": false, "dependencies": ["package_info_plus"], "dev_dependency": false}], "web": [{"name": "agora_rtc_engine", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\agora_rtc_engine-6.5.2\\\\", "dependencies": ["iris_method_channel"], "dev_dependency": false}, {"name": "audioplayers_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\audioplayers_web-4.1.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-4.0.2\\\\", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\device_info_plus-9.1.2\\\\", "dependencies": [], "dev_dependency": false}, {"name": "firebase_core_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core_web-2.17.5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_messaging_web-3.8.7\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "flutter_keyboard_visibility_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_keyboard_visibility_web-2.0.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "geolocator_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_web-2.2.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_sign_in_web-0.12.4+4\\\\", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\image_picker_for_web-2.2.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "iris_method_channel", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\iris_method_channel-2.2.2\\\\", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-4.2.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_html-0.1.3+5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "pointer_interceptor_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\pointer_interceptor_web-0.10.2+1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "record_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\record_web-1.1.6\\\\", "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\share_plus-7.2.2\\\\", "dependencies": ["url_launcher_web"], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}, {"name": "sign_in_with_apple_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sign_in_with_apple_web-2.1.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_web-2.4.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "video_player_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_web-2.3.5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\wakelock_plus-1.2.1\\\\", "dependencies": ["package_info_plus"], "dev_dependency": false}]}, "dependencyGraph": [{"name": "agora_rtc_engine", "dependencies": ["iris_method_channel"]}, {"name": "audioplayers", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_web", "audioplayers_windows", "path_provider"]}, {"name": "audioplayers_android", "dependencies": []}, {"name": "audioplayers_darwin", "dependencies": []}, {"name": "audioplayers_linux", "dependencies": []}, {"name": "audioplayers_web", "dependencies": []}, {"name": "audioplayers_windows", "dependencies": []}, {"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_stripe", "dependencies": ["stripe_android", "stripe_ios"]}, {"name": "geocoding", "dependencies": ["geocoding_android", "geocoding_ios"]}, {"name": "geocoding_android", "dependencies": []}, {"name": "geocoding_ios", "dependencies": []}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "iris_method_channel", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "pointer_interceptor", "dependencies": ["pointer_interceptor_ios", "pointer_interceptor_web"]}, {"name": "pointer_interceptor_ios", "dependencies": []}, {"name": "pointer_interceptor_web", "dependencies": []}, {"name": "record", "dependencies": ["record_web", "record_windows", "record_linux", "record_android", "record_darwin"]}, {"name": "record_android", "dependencies": []}, {"name": "record_darwin", "dependencies": []}, {"name": "record_linux", "dependencies": []}, {"name": "record_web", "dependencies": []}, {"name": "record_windows", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sign_in_with_apple", "dependencies": ["sign_in_with_apple_web"]}, {"name": "sign_in_with_apple_web", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "stripe_android", "dependencies": []}, {"name": "stripe_ios", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}], "date_created": "2025-06-13 02:48:39.662415", "version": "3.29.3", "swift_package_manager_enabled": {"ios": false, "macos": false}}